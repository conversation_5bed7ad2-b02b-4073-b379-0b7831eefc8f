#!/usr/bin/env python3
import requests
import json

try:
    response = requests.get("http://0.0.0.0:7860/health/")
    if response.status_code == 200:
        health = response.json()
        print("Server Health:")
        print(json.dumps(health, indent=2))
        
        if health.get('model_pool_initialized'):
            print("\n✅ Model pool is initialized")
            print(f"Available models: {health.get('available_models')}/{health.get('total_models')}")
        else:
            print("\n❌ Model pool NOT initialized")
    else:
        print(f"Error: HTTP {response.status_code}")
        print(response.text)
except Exception as e:
    print(f"Error: {e}")
