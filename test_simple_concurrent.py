#!/usr/bin/env python3
"""
Test đơn giản để kiểm tra concurrency với 2 requests
"""

import asyncio
import aiohttp
import time

async def make_request(session, request_id):
    """G<PERSON>i một request và in thời gian"""
    start_time = time.time()
    
    params = {
        'text': f'Synthesize speech using a specific voice and style.',
        'voice': 'en',  # <PERSON><PERSON> dụng voice 'en' như trong log
    }
    
    print(f"[Request {request_id}] Starting at {time.time():.2f}")
    
    try:
        async with session.get("http://0.0.0.0:7860/synthesize_speech/", params=params) as response:
            if response.status == 200:
                content = await response.read()
                end_time = time.time()
                elapsed = end_time - start_time
                
                print(f"[Request {request_id}] Completed at {end_time:.2f} (took {elapsed:.2f}s)")
                return {'success': True, 'time': elapsed, 'start': start_time, 'end': end_time}
            else:
                print(f"[Request {request_id}] Failed: HTTP {response.status}")
                return {'success': False}
    except Exception as e:
        print(f"[Request {request_id}] Error: {e}")
        return {'success': False}

async def test_concurrent():
    """Test 2 requests đồng thời"""
    print("Testing 2 concurrent requests...")
    print("=" * 40)
    
    # Kiểm tra health trước
    async with aiohttp.ClientSession() as session:
        try:
            async with session.get("http://localhost:7860/health/") as response:
                if response.status == 200:
                    health = await response.json()
                    print(f"Server health: {health}")
                    if not health.get('model_pool_initialized'):
                        print("⚠️ Model pool not initialized!")
                        return
                else:
                    print(f"Health check failed: HTTP {response.status}")
                    return
        except Exception as e:
            print(f"Cannot connect to server: {e}")
            return
    
    # Chạy 2 requests đồng thời
    async with aiohttp.ClientSession() as session:
        overall_start = time.time()
        
        # Tạo 2 tasks
        task1 = make_request(session, 1)
        task2 = make_request(session, 2)
        
        # Chạy đồng thời
        results = await asyncio.gather(task1, task2)
        
        overall_end = time.time()
        overall_time = overall_end - overall_start
        
        print("=" * 40)
        print("RESULTS:")
        
        successful = [r for r in results if r.get('success')]
        
        if len(successful) == 2:
            times = [r['time'] for r in successful]
            starts = [r['start'] for r in successful]
            ends = [r['end'] for r in successful]
            
            print(f"Both requests successful!")
            print(f"Request 1: {times[0]:.2f}s")
            print(f"Request 2: {times[1]:.2f}s")
            print(f"Overall time: {overall_time:.2f}s")
            print(f"Sum of individual times: {sum(times):.2f}s")
            
            # Kiểm tra overlap
            start_diff = abs(starts[0] - starts[1])
            end_diff = abs(ends[0] - ends[1])
            
            print(f"Start time difference: {start_diff:.2f}s")
            print(f"End time difference: {end_diff:.2f}s")
            
            if overall_time < sum(times) * 0.8:
                print("✅ Requests processed CONCURRENTLY!")
            else:
                print("❌ Requests processed SEQUENTIALLY")
                
            if start_diff < 0.1:
                print("✅ Requests started almost simultaneously")
            else:
                print("⚠️ Requests did not start simultaneously")
                
        else:
            print(f"Only {len(successful)}/2 requests successful")

if __name__ == "__main__":
    asyncio.run(test_concurrent())
