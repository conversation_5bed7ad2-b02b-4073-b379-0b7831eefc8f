#!/usr/bin/env python3
"""
Test đơn giản để kiểm tra concurrency với 2 requests
"""

import asyncio
import aiohttp
import time

async def make_request(session, request_id):
    """G<PERSON>i một request và in thời gian"""
    start_time = time.time()
    
    params = {
        'text': f'Synthesize speech using a specific voice and style.',
        'voice': 'en',  # <PERSON><PERSON> dụng voice 'en' như trong log
    }
    
    print(f"[Request {request_id}] Starting at {time.time():.2f}")
    
    try:
        async with session.get("http://localhost:7860/synthesize_speech/", params=params) as response:
            if response.status == 200:
                content = await response.read()
                end_time = time.time()
                elapsed = end_time - start_time
                
                print(f"[Request {request_id}] Completed at {end_time:.2f} (took {elapsed:.2f}s)")
                return {'success': True, 'time': elapsed, 'start': start_time, 'end': end_time}
            else:
                print(f"[Request {request_id}] Failed: HTTP {response.status}")
                return {'success': False}
    except Exception as e:
        print(f"[Request {request_id}] Error: {e}")
        return {'success': False}

async def test_concurrent():
    """Test 5 requests đồng thời"""
    print("Testing 5 concurrent requests...")
    print("=" * 40)
    
    # Kiểm tra health trước
    async with aiohttp.ClientSession() as session:
        try:
            async with session.get("http://localhost:7860/health/") as response:
                if response.status == 200:
                    health = await response.json()
                    print(f"Server health: {health}")
                    if not health.get('model_pool_initialized'):
                        print("⚠️ Model pool not initialized!")
                        return
                else:
                    print(f"Health check failed: HTTP {response.status}")
                    return
        except Exception as e:
            print(f"Cannot connect to server: {e}")
            return
    
    # Chạy 5 requests đồng thời
    async with aiohttp.ClientSession() as session:
        overall_start = time.time()

        # Tạo 5 tasks
        tasks = [make_request(session, i+1) for i in range(5)]

        # Chạy đồng thời
        results = await asyncio.gather(*tasks)
        
        overall_end = time.time()
        overall_time = overall_end - overall_start
        
        print("=" * 40)
        print("RESULTS:")
        
        successful = [r for r in results if r.get('success')]

        if len(successful) >= 3:  # Ít nhất 3/5 requests thành công
            times = [r['time'] for r in successful]
            starts = [r['start'] for r in successful]
            ends = [r['end'] for r in successful]

            print(f"{len(successful)}/5 requests successful!")
            for i, t in enumerate(times):
                print(f"Request {i+1}: {t:.2f}s")

            print(f"Overall time: {overall_time:.2f}s")
            print(f"Sum of individual times: {sum(times):.2f}s")

            # Kiểm tra concurrency
            max_start_diff = max(starts) - min(starts)
            max_end_diff = max(ends) - min(ends)

            print(f"Max start time difference: {max_start_diff:.2f}s")
            print(f"Max end time difference: {max_end_diff:.2f}s")

            # Tính speedup
            if len(successful) > 0:
                avg_individual_time = sum(times) / len(times)
                theoretical_sequential_time = avg_individual_time * len(successful)
                speedup = theoretical_sequential_time / overall_time

                print(f"Average individual time: {avg_individual_time:.2f}s")
                print(f"Theoretical sequential time: {theoretical_sequential_time:.2f}s")
                print(f"Speedup: {speedup:.2f}x")

                if speedup > 1.5:
                    print("✅ Significant CONCURRENT processing!")
                elif speedup > 1.2:
                    print("✅ Some concurrent processing")
                else:
                    print("❌ Mostly sequential processing")

            if max_start_diff < 0.5:
                print("✅ Requests started almost simultaneously")
            else:
                print("⚠️ Requests had staggered starts")

        else:
            print(f"Only {len(successful)}/5 requests successful")

if __name__ == "__main__":
    asyncio.run(test_concurrent())
