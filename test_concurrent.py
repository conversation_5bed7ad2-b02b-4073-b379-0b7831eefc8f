#!/usr/bin/env python3
"""
Test script để kiểm tra khả năng xử lý concurrent requests của F5-TTS server
"""

import asyncio
import aiohttp
import time
import json
from typing import List, Dict

# C<PERSON><PERSON> h<PERSON>nh test
SERVER_URL = "http://localhost:8000"
NUM_CONCURRENT_REQUESTS = 10
TEST_TEXTS = [
    "Hello, this is a test message number one.",
    "This is the second test message for concurrent processing.",
    "Testing concurrent speech synthesis with message three.",
    "Fourth message to test parallel processing capabilities.",
    "Fifth test message for evaluating server performance.",
    "Sixth message testing the model pool functionality.",
    "Seventh test to check if requests are processed in parallel.",
    "Eighth message for concurrent request handling evaluation.",
    "Ninth test message to verify async processing works.",
    "Tenth and final message for concurrent testing."
]

async def make_request(session: aiohttp.ClientSession, request_id: int, text: str) -> Dict:
    """G<PERSON>i một request TTS và đo thời gian"""
    start_time = time.time()
    
    try:
        params = {
            'text': text,
            'voice': 'default_en',
            'speed': 1.0
        }
        
        print(f"[Request {request_id}] Starting at {start_time:.2f}")
        
        async with session.get(f"{SERVER_URL}/synthesize_speech/", params=params) as response:
            if response.status == 200:
                # Đọc response để hoàn thành request
                content = await response.read()
                end_time = time.time()
                elapsed = end_time - start_time
                
                # Lấy thông tin từ headers
                server_elapsed = response.headers.get('X-Elapsed-Time', 'N/A')
                request_id_header = response.headers.get('X-Request-ID', 'N/A')
                
                print(f"[Request {request_id}] Completed in {elapsed:.2f}s (server: {server_elapsed}s, ID: {request_id_header})")
                
                return {
                    'request_id': request_id,
                    'success': True,
                    'client_time': elapsed,
                    'server_time': server_elapsed,
                    'server_request_id': request_id_header,
                    'start_time': start_time,
                    'end_time': end_time,
                    'content_length': len(content)
                }
            else:
                error_text = await response.text()
                print(f"[Request {request_id}] Failed with status {response.status}: {error_text}")
                return {
                    'request_id': request_id,
                    'success': False,
                    'error': f"HTTP {response.status}: {error_text}",
                    'client_time': time.time() - start_time
                }
                
    except Exception as e:
        elapsed = time.time() - start_time
        print(f"[Request {request_id}] Exception after {elapsed:.2f}s: {str(e)}")
        return {
            'request_id': request_id,
            'success': False,
            'error': str(e),
            'client_time': elapsed
        }

async def check_server_health(session: aiohttp.ClientSession) -> Dict:
    """Kiểm tra trạng thái server"""
    try:
        async with session.get(f"{SERVER_URL}/health/") as response:
            if response.status == 200:
                return await response.json()
            else:
                return {"error": f"HTTP {response.status}"}
    except Exception as e:
        return {"error": str(e)}

async def run_concurrent_test():
    """Chạy test concurrent requests"""
    print(f"Testing concurrent requests to {SERVER_URL}")
    print(f"Number of concurrent requests: {NUM_CONCURRENT_REQUESTS}")
    print("=" * 60)
    
    async with aiohttp.ClientSession() as session:
        # Kiểm tra trạng thái server trước khi test
        print("Checking server health...")
        health = await check_server_health(session)
        print(f"Server health: {json.dumps(health, indent=2)}")
        
        if not health.get('model_pool_initialized', False):
            print("⚠️  Model pool not initialized yet. Waiting 30 seconds...")
            await asyncio.sleep(30)
            health = await check_server_health(session)
            print(f"Server health after wait: {json.dumps(health, indent=2)}")
        
        print("\nStarting concurrent requests...")
        print("=" * 60)
        
        # Tạo tasks cho tất cả requests
        tasks = []
        overall_start = time.time()
        
        for i in range(NUM_CONCURRENT_REQUESTS):
            text = TEST_TEXTS[i % len(TEST_TEXTS)]
            task = make_request(session, i + 1, text)
            tasks.append(task)
        
        # Chạy tất cả requests đồng thời
        results = await asyncio.gather(*tasks, return_exceptions=True)
        overall_end = time.time()
        overall_elapsed = overall_end - overall_start
        
        print("=" * 60)
        print("RESULTS SUMMARY")
        print("=" * 60)
        
        successful_requests = [r for r in results if isinstance(r, dict) and r.get('success', False)]
        failed_requests = [r for r in results if isinstance(r, dict) and not r.get('success', False)]
        exceptions = [r for r in results if not isinstance(r, dict)]
        
        print(f"Total requests: {NUM_CONCURRENT_REQUESTS}")
        print(f"Successful: {len(successful_requests)}")
        print(f"Failed: {len(failed_requests)}")
        print(f"Exceptions: {len(exceptions)}")
        print(f"Overall time: {overall_elapsed:.2f}s")
        
        if successful_requests:
            client_times = [float(r['client_time']) for r in successful_requests]
            server_times = [float(r['server_time']) for r in successful_requests if r['server_time'] != 'N/A']
            
            print(f"\nClient-side timing:")
            print(f"  Min: {min(client_times):.2f}s")
            print(f"  Max: {max(client_times):.2f}s")
            print(f"  Avg: {sum(client_times)/len(client_times):.2f}s")
            
            if server_times:
                print(f"\nServer-side timing:")
                print(f"  Min: {min(server_times):.2f}s")
                print(f"  Max: {max(server_times):.2f}s")
                print(f"  Avg: {sum(server_times)/len(server_times):.2f}s")
            
            # Kiểm tra xem có xử lý song song không
            start_times = [r['start_time'] for r in successful_requests]
            end_times = [r['end_time'] for r in successful_requests]
            
            earliest_start = min(start_times)
            latest_end = max(end_times)
            total_processing_time = latest_end - earliest_start
            
            print(f"\nConcurrency analysis:")
            print(f"  Time from first start to last end: {total_processing_time:.2f}s")
            print(f"  Sum of all individual times: {sum(client_times):.2f}s")
            
            if total_processing_time < sum(client_times) * 0.8:
                print("  ✅ Requests appear to be processed concurrently!")
            else:
                print("  ❌ Requests appear to be processed sequentially.")
        
        # Hiển thị chi tiết các request thất bại
        if failed_requests:
            print(f"\nFailed requests details:")
            for req in failed_requests:
                print(f"  Request {req['request_id']}: {req.get('error', 'Unknown error')}")
        
        if exceptions:
            print(f"\nExceptions:")
            for i, exc in enumerate(exceptions):
                print(f"  Exception {i+1}: {exc}")

if __name__ == "__main__":
    asyncio.run(run_concurrent_test())
