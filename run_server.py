#!/usr/bin/env python3
"""
Script để chạy F5-TTS server với cấu hình tối ưu cho concurrent processing
"""

import uvicorn
import os
import sys

def main():
    # Cấu hình environment variables cho tối ưu hóa
    os.environ["TOKENIZERS_PARALLELISM"] = "false"  # Tránh warning từ tokenizers
    os.environ["OMP_NUM_THREADS"] = "4"  # Giới hạn OpenMP threads
    
    # Thêm thư mục hiện tại vào Python path
    current_dir = os.path.dirname(os.path.abspath(__file__))
    if current_dir not in sys.path:
        sys.path.insert(0, current_dir)
    
    print("Starting F5-TTS Server with concurrent processing support...")
    print("Server features:")
    print("- Model pool with 3 concurrent models")
    print("- Async request processing")
    print("- Thread pool for CPU-bound operations")
    print("- Unique request IDs for tracking")
    print("- Health check endpoint at /health/")
    print()
    
    # Chạy server với cấu hình tối ưu
    uvicorn.run(
        "f5-tts_server.server:app",
        host="0.0.0.0",
        port=7860,  # Sử dụng port 7860 như yêu cầu
        workers=1,  # QUAN TRỌNG: Chỉ dùng 1 worker vì model pool đã xử lý concurrency
        loop="asyncio",
        access_log=True,
        log_level="info",
        reload=False,  # Tắt reload để tránh conflict với model pool
        # Tối ưu hóa cho high concurrency
        limit_concurrency=50,  # Giới hạn số request đồng thời
        limit_max_requests=1000,  # Restart worker sau 1000 requests để tránh memory leak
        timeout_keep_alive=30,  # Keep-alive timeout
        timeout_graceful_shutdown=30  # Graceful shutdown timeout
    )

if __name__ == "__main__":
    main()
