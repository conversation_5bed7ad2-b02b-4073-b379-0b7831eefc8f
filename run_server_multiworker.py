#!/usr/bin/env python3
"""
Script để chạy F5-TTS server với multiple workers
⚠️ CẢNH BÁO: Chỉ sử dụng nếu có đủ GPU memory (>= 24GB)
"""

import uvicorn
import os
import sys

def main():
    # Cấu hình environment variables
    os.environ["TOKENIZERS_PARALLELISM"] = "false"
    os.environ["OMP_NUM_THREADS"] = "2"  # Giảm threads per worker
    
    current_dir = os.path.dirname(os.path.abspath(__file__))
    if current_dir not in sys.path:
        sys.path.insert(0, current_dir)
    
    print("⚠️  CẢNH BÁO: Chạy với multiple workers")
    print("Yêu cầu:")
    print("- GPU memory >= 24GB")
    print("- Mỗi worker sẽ tạo 5 models = ~15GB GPU memory")
    print("- 2 workers = ~30GB GPU memory")
    print()
    
    # Kiểm tra GPU memory
    try:
        import torch
        if torch.cuda.is_available():
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
            print(f"GPU Memory: {gpu_memory:.1f}GB")
            
            if gpu_memory < 20:
                print("❌ Không đủ GPU memory cho multiple workers!")
                print("Khuyến nghị: Sử dụng run_server.py thay thế")
                return
        else:
            print("❌ Không tìm thấy GPU!")
            return
    except ImportError:
        print("⚠️  Không thể kiểm tra GPU memory")
    
    print("Starting F5-TTS Server with multiple workers...")
    
    # Chạy với 2 workers
    uvicorn.run(
        "f5-tts_server.server:app",
        host="0.0.0.0",
        port=7860,
        workers=2,  # 2 workers - mỗi worker có 5 models
        loop="asyncio",
        access_log=True,
        log_level="info",
        reload=False,
        # Cấu hình cho multiple workers
        limit_concurrency=100,  # Tăng limit vì có nhiều workers
        limit_max_requests=500,  # Giảm để restart workers thường xuyên hơn
        timeout_keep_alive=60,
        timeout_graceful_shutdown=60,
        # Worker timeout
        timeout_worker=300  # 5 phút timeout cho worker initialization
    )

if __name__ == "__main__":
    main()
