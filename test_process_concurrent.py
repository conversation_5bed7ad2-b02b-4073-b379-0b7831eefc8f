#!/usr/bin/env python3
"""
Test process-based concurrent processing
"""

import asyncio
import aiohttp
import time
import psutil
import subprocess

async def get_gpu_usage():
    """Lấy GPU usage"""
    try:
        result = subprocess.run([
            'nvidia-smi', 
            '--query-gpu=utilization.gpu,memory.used,memory.total',
            '--format=csv,noheader,nounits'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            parts = result.stdout.strip().split(', ')
            return {
                'gpu_util': int(parts[0]),
                'memory_used': int(parts[1]),
                'memory_total': int(parts[2])
            }
    except:
        pass
    return None

async def make_request(session, request_id):
    """Gửi một request TTS"""
    start_time = time.time()
    
    params = {
        'text': f'Testing process-based concurrent processing with request {request_id}',
        'voice': 'en',
        'speed': 1.0
    }
    
    print(f"[Request {request_id}] Starting at {time.time():.2f}")
    
    try:
        async with session.get("http://localhost:7860/synthesize_speech/", params=params) as response:
            if response.status == 200:
                content = await response.read()
                end_time = time.time()
                elapsed = end_time - start_time
                
                print(f"[Request {request_id}] Completed at {end_time:.2f} (took {elapsed:.2f}s)")
                return {
                    'request_id': request_id,
                    'success': True,
                    'time': elapsed,
                    'start': start_time,
                    'end': end_time,
                    'size': len(content)
                }
            else:
                print(f"[Request {request_id}] Failed: HTTP {response.status}")
                return {'request_id': request_id, 'success': False}
    except Exception as e:
        print(f"[Request {request_id}] Error: {e}")
        return {'request_id': request_id, 'success': False}

async def monitor_system_during_test():
    """Monitor system resources during test"""
    print("\nSystem monitoring started...")
    max_gpu_util = 0
    max_cpu_util = 0
    
    for i in range(30):  # Monitor for 30 seconds
        cpu_percent = psutil.cpu_percent(interval=0.1)
        gpu_info = await get_gpu_usage()
        
        if gpu_info:
            gpu_util = gpu_info['gpu_util']
            max_gpu_util = max(max_gpu_util, gpu_util)
            print(f"[Monitor] CPU: {cpu_percent:.1f}%, GPU: {gpu_util}%, GPU Mem: {gpu_info['memory_used']}/{gpu_info['memory_total']}MB")
        else:
            print(f"[Monitor] CPU: {cpu_percent:.1f}%, GPU: N/A")
        
        max_cpu_util = max(max_cpu_util, cpu_percent)
        await asyncio.sleep(1)
    
    return max_cpu_util, max_gpu_util

async def test_process_concurrent():
    """Test concurrent processing với process pool"""
    print("Testing Process-Based Concurrent Processing")
    print("=" * 50)
    
    # Kiểm tra server health
    async with aiohttp.ClientSession() as session:
        try:
            async with session.get("http://localhost:7860/health/") as response:
                if response.status == 200:
                    health = await response.json()
                    print(f"Server health: {health}")
                else:
                    print(f"Health check failed: HTTP {response.status}")
                    return
        except Exception as e:
            print(f"Cannot connect to server: {e}")
            return
    
    # Test với 3 requests đồng thời
    print(f"\nTesting 3 concurrent requests...")
    print("-" * 30)
    
    async with aiohttp.ClientSession() as session:
        overall_start = time.time()
        
        # Tạo tasks
        request_tasks = [make_request(session, i+1) for i in range(3)]
        
        # Chạy monitoring và requests đồng thời
        monitor_task = monitor_system_during_test()
        
        # Chạy tất cả
        results, (max_cpu, max_gpu) = await asyncio.gather(
            asyncio.gather(*request_tasks),
            monitor_task
        )
        
        overall_end = time.time()
        overall_time = overall_end - overall_start
        
        print("\n" + "=" * 50)
        print("RESULTS:")
        print("=" * 50)
        
        successful = [r for r in results if r.get('success')]
        
        if len(successful) >= 2:
            times = [r['time'] for r in successful]
            starts = [r['start'] for r in successful]
            ends = [r['end'] for r in successful]
            
            print(f"Successful requests: {len(successful)}/3")
            for i, r in enumerate(successful):
                print(f"Request {r['request_id']}: {r['time']:.2f}s")
            
            print(f"\nTiming Analysis:")
            print(f"Overall time: {overall_time:.2f}s")
            print(f"Sum of individual times: {sum(times):.2f}s")
            print(f"Average individual time: {sum(times)/len(times):.2f}s")
            
            # Kiểm tra concurrency
            start_spread = max(starts) - min(starts)
            end_spread = max(ends) - min(ends)
            
            print(f"Start time spread: {start_spread:.2f}s")
            print(f"End time spread: {end_spread:.2f}s")
            
            # Tính speedup
            theoretical_sequential = sum(times)
            speedup = theoretical_sequential / overall_time
            
            print(f"\nPerformance:")
            print(f"Theoretical sequential time: {theoretical_sequential:.2f}s")
            print(f"Actual concurrent time: {overall_time:.2f}s")
            print(f"Speedup: {speedup:.2f}x")
            
            # System utilization
            print(f"\nSystem Utilization:")
            print(f"Max CPU usage: {max_cpu:.1f}%")
            print(f"Max GPU usage: {max_gpu}%")
            
            # Đánh giá
            if speedup > 2.5:
                print("🚀 EXCELLENT process-based concurrency!")
            elif speedup > 2.0:
                print("✅ GOOD process-based concurrency!")
            elif speedup > 1.5:
                print("👍 MODERATE concurrency")
            else:
                print("❌ POOR concurrency - still sequential?")
                
            if max_gpu > 80:
                print("✅ GPU is being utilized well!")
            elif max_gpu > 50:
                print("👍 GPU utilization is moderate")
            else:
                print("⚠️ GPU utilization is low")
                
        else:
            print(f"Only {len(successful)}/3 requests successful")

if __name__ == "__main__":
    asyncio.run(test_process_concurrent())
