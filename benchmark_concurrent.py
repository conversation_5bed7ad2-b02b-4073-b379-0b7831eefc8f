#!/usr/bin/env python3
"""
Benchmark script để test performance v<PERSON><PERSON> số lượ<PERSON> requests khác nhau
"""

import asyncio
import aiohttp
import time
import statistics

async def make_request(session, request_id, text="Test concurrent processing"):
    """<PERSON><PERSON><PERSON> một request TTS"""
    start_time = time.time()
    
    params = {
        'text': f'{text} - Request {request_id}',
        'voice': 'en',
        'speed': 1.0
    }
    
    try:
        async with session.get("http://localhost:7860/synthesize_speech/", params=params) as response:
            if response.status == 200:
                content = await response.read()
                end_time = time.time()
                elapsed = end_time - start_time
                
                return {
                    'request_id': request_id,
                    'success': True,
                    'time': elapsed,
                    'start': start_time,
                    'end': end_time,
                    'size': len(content)
                }
            else:
                return {'request_id': request_id, 'success': False, 'error': f'HTTP {response.status}'}
    except Exception as e:
        return {'request_id': request_id, 'success': False, 'error': str(e)}

async def benchmark_concurrent_requests(num_requests):
    """Benchmark với số lượng requests cụ thể"""
    print(f"\n{'='*50}")
    print(f"TESTING {num_requests} CONCURRENT REQUESTS")
    print(f"{'='*50}")
    
    async with aiohttp.ClientSession() as session:
        # Kiểm tra server health
        try:
            async with session.get("http://localhost:7860/health/") as response:
                if response.status == 200:
                    health = await response.json()
                    print(f"Available models: {health.get('available_models')}/{health.get('total_models')}")
                else:
                    print("⚠️ Cannot check server health")
        except:
            print("⚠️ Server health check failed")
        
        # Tạo và chạy tasks
        overall_start = time.time()
        tasks = [make_request(session, i+1) for i in range(num_requests)]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        overall_end = time.time()
        
        # Phân tích kết quả
        successful = [r for r in results if isinstance(r, dict) and r.get('success')]
        failed = [r for r in results if isinstance(r, dict) and not r.get('success')]
        exceptions = [r for r in results if not isinstance(r, dict)]
        
        overall_time = overall_end - overall_start
        
        print(f"\nRESULTS:")
        print(f"Successful: {len(successful)}/{num_requests}")
        print(f"Failed: {len(failed)}")
        print(f"Exceptions: {len(exceptions)}")
        print(f"Overall time: {overall_time:.2f}s")
        
        if successful:
            times = [r['time'] for r in successful]
            starts = [r['start'] for r in successful]
            ends = [r['end'] for r in successful]
            
            # Thống kê thời gian
            avg_time = statistics.mean(times)
            min_time = min(times)
            max_time = max(times)
            
            print(f"\nTiming Statistics:")
            print(f"Average time: {avg_time:.2f}s")
            print(f"Min time: {min_time:.2f}s")
            print(f"Max time: {max_time:.2f}s")
            print(f"Std deviation: {statistics.stdev(times) if len(times) > 1 else 0:.2f}s")
            
            # Phân tích concurrency
            theoretical_sequential = avg_time * len(successful)
            speedup = theoretical_sequential / overall_time
            efficiency = speedup / num_requests * 100
            
            print(f"\nConcurrency Analysis:")
            print(f"Theoretical sequential time: {theoretical_sequential:.2f}s")
            print(f"Actual time: {overall_time:.2f}s")
            print(f"Speedup: {speedup:.2f}x")
            print(f"Efficiency: {efficiency:.1f}%")
            
            # Phân tích timing overlap
            start_spread = max(starts) - min(starts)
            end_spread = max(ends) - min(ends)
            
            print(f"\nTiming Spread:")
            print(f"Start time spread: {start_spread:.2f}s")
            print(f"End time spread: {end_spread:.2f}s")
            
            # Đánh giá
            if speedup > num_requests * 0.7:
                print("🚀 EXCELLENT concurrency!")
            elif speedup > num_requests * 0.5:
                print("✅ GOOD concurrency")
            elif speedup > 1.5:
                print("👍 MODERATE concurrency")
            else:
                print("❌ POOR concurrency")
        
        # Hiển thị lỗi nếu có
        if failed:
            print(f"\nFailed requests:")
            for f in failed[:3]:  # Chỉ hiển thị 3 lỗi đầu
                print(f"  Request {f['request_id']}: {f.get('error', 'Unknown error')}")
        
        return {
            'num_requests': num_requests,
            'successful': len(successful),
            'overall_time': overall_time,
            'avg_time': statistics.mean(times) if times else 0,
            'speedup': speedup if successful else 0
        }

async def run_benchmark():
    """Chạy benchmark với các số lượng requests khác nhau"""
    print("F5-TTS Concurrent Processing Benchmark")
    print("=" * 50)
    
    # Test với các số lượng requests khác nhau
    test_cases = [1, 2, 3, 5, 8, 10]
    results = []
    
    for num_requests in test_cases:
        try:
            result = await benchmark_concurrent_requests(num_requests)
            results.append(result)
            
            # Nghỉ giữa các test
            if num_requests < test_cases[-1]:
                print(f"\nWaiting 5 seconds before next test...")
                await asyncio.sleep(5)
                
        except KeyboardInterrupt:
            print("\nBenchmark interrupted by user")
            break
        except Exception as e:
            print(f"\nError in benchmark: {e}")
    
    # Tóm tắt kết quả
    if results:
        print(f"\n{'='*60}")
        print("BENCHMARK SUMMARY")
        print(f"{'='*60}")
        print(f"{'Requests':<10} {'Success':<8} {'Time':<8} {'Avg':<8} {'Speedup':<8}")
        print("-" * 50)
        
        for r in results:
            print(f"{r['num_requests']:<10} {r['successful']:<8} {r['overall_time']:<8.2f} {r['avg_time']:<8.2f} {r['speedup']:<8.2f}x")
        
        # Tìm sweet spot
        best_efficiency = max(results, key=lambda x: x['speedup'] / x['num_requests'] if x['num_requests'] > 0 else 0)
        print(f"\nBest efficiency: {best_efficiency['num_requests']} concurrent requests")

if __name__ == "__main__":
    asyncio.run(run_benchmark())
