#!/usr/bin/env python3
"""
Test script để kiểm tra syntax của server
"""

import sys
import os

# Thêm thư mục hiện tại vào Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

try:
    print("Testing server syntax...")
    import importlib.util
    
    # Load module từ file
    spec = importlib.util.spec_from_file_location("server", "f5-tts_server/server.py")
    server_module = importlib.util.module_from_spec(spec)
    
    print("✅ Server syntax is OK!")
    print("✅ All imports successful!")
    
except SyntaxError as e:
    print(f"❌ Syntax Error: {e}")
    sys.exit(1)
except ImportError as e:
    print(f"⚠️  Import Error (expected): {e}")
    print("✅ Server syntax is OK, but some dependencies may be missing")
except Exception as e:
    print(f"❌ Unexpected Error: {e}")
    sys.exit(1)

print("Server is ready to run!")
