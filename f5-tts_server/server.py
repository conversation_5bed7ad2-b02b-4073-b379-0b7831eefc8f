import os
import time
import torch
from fastapi import Fast<PERSON><PERSON>, UploadFile, File, HTTPException, Form
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse, FileResponse
from typing import Optional
import torchaudio
import soundfile as sf
from pydub import AudioSegment, silence
import re
from importlib.resources import files
from cached_path import cached_path
import sys
import logging
import io
import magic
from pydantic import BaseModel
import time
import asyncio
import threading
from concurrent.futures import ThreadPoolExecutor
import uuid

# Add F5-TTS root directory to path so we can import modules
sys.path.append("/workspace/F5-TTS")

from f5_tts.api import F5TTS

logging.basicConfig(level=logging.INFO)

app = FastAPI()

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

device = "cuda:0" if torch.cuda.is_available() else "cpu"

# Thread-safe model pool for concurrent processing
class ModelPool:
    def __init__(self, pool_size=3):
        self.pool_size = pool_size
        self.models = []
        self.available_models = asyncio.Queue()
        self.lock = threading.Lock()
        self._initialized = False

    def initialize(self):
        """Initialize model pool - called once at startup"""
        if self._initialized:
            return

        with self.lock:
            if self._initialized:
                return

            logging.info(f"Initializing model pool with {self.pool_size} models...")
            for i in range(self.pool_size):
                model = F5TTS(
                    device=device,
                    model_type="F5-TTS",
                    vocab_file=str(cached_path("hf://SWivid/F5-TTS/F5TTS_Base/vocab.txt")),
                    ode_method="euler",
                    use_ema=True,
                    vocoder_name="vocos",
                    ckpt_file=str(cached_path("hf://SWivid/F5-TTS/F5TTS_Base/model_1200000.safetensors"))
                )
                self.models.append(model)
                # Add to available queue synchronously during initialization
                self.available_models.put_nowait(model)
                logging.info(f"Model {i+1}/{self.pool_size} initialized")

            self._initialized = True
            logging.info("Model pool initialization complete")

    async def get_model(self):
        """Get an available model from the pool"""
        if not self._initialized:
            self.initialize()

        available_count = self.available_models.qsize()
        logging.info(f"Getting model from pool. Available: {available_count}/{self.pool_size}")

        model = await self.available_models.get()
        remaining_count = self.available_models.qsize()
        logging.info(f"Model acquired. Remaining: {remaining_count}/{self.pool_size}")

        return model

    async def return_model(self, model):
        """Return a model to the pool"""
        await self.available_models.put(model)
        available_count = self.available_models.qsize()
        logging.info(f"Model returned to pool. Available: {available_count}/{self.pool_size}")

# Initialize model pool - tăng lên 3 models cho performance tốt hơn
model_pool = ModelPool(pool_size=3)  # 3 models để xử lý 5 requests đồng thời

# Thread pool for CPU-bound operations - tăng workers
thread_pool = ThreadPoolExecutor(max_workers=8)  # Tăng để xử lý preprocessing song song

output_dir = 'outputs'
os.makedirs(output_dir, exist_ok=True)

# Copy the English reference audio to resources if it doesn't exist
resources_dir = 'resources'
os.makedirs(resources_dir, exist_ok=True)
default_ref_audio = str(files("f5_tts").joinpath("infer/examples/basic/basic_ref_en.wav"))
default_ref_text = "Some call me nature, others call me mother nature."

if not os.path.exists(f"{resources_dir}/default_en.wav"):
    import shutil
    shutil.copy2(default_ref_audio, f"{resources_dir}/default_en.wav")

os.makedirs("resources", exist_ok=True)

def convert_to_wav(input_path, output_path):
    """Convert any audio format to WAV using pydub."""
    audio = AudioSegment.from_file(input_path)
    audio = audio.set_channels(1)  # Convert to mono
    audio = audio.set_frame_rate(24000)  # Set to F5-TTS expected sample rate
    audio.export(output_path, format='wav')

async def run_in_thread(func, *args, **kwargs):
    """Run a CPU-bound function in thread pool"""
    loop = asyncio.get_event_loop()
    return await loop.run_in_executor(thread_pool, func, *args, **kwargs)

def split_text_into_sentences(text):
    """Split text into sentences using regex."""
    # Split on common sentence endings
    sentences = re.split(r'(?<=[.!?])\s+', text)
    # Remove empty sentences and extra whitespace
    sentences = [s.strip() for s in sentences if s.strip()]
    return sentences

def detect_leading_silence(audio, silence_threshold=-42, chunk_size=10):
    """Detect silence at the beginning of the audio."""
    trim_ms = 0
    while audio[trim_ms:trim_ms + chunk_size].dBFS < silence_threshold and trim_ms < len(audio):
        trim_ms += chunk_size
    return trim_ms

def remove_silence_edges(audio, silence_threshold=-42):
    """Remove silence from the beginning and end of the audio."""
    start_trim = detect_leading_silence(audio, silence_threshold)
    end_trim = detect_leading_silence(audio.reverse(), silence_threshold)
    duration = len(audio)
    return audio[start_trim:duration - end_trim]

class UploadAudioRequest(BaseModel):
    audio_file_label: str

@app.on_event("startup")
async def startup_event():
    """Initialize model pool on startup"""
    logging.info("Starting server initialization...")
    # Initialize model pool in background thread to avoid blocking startup
    loop = asyncio.get_event_loop()
    await loop.run_in_executor(thread_pool, model_pool.initialize)
    logging.info("Server startup complete")

@app.get("/health/")
async def health_check():
    """
    Check server health and model pool status.
    """
    return {
        "status": "healthy",
        "model_pool_initialized": model_pool._initialized,
        "total_models": model_pool.pool_size,
        "available_models": model_pool.available_models.qsize(),
        "device": device
    }

@app.get("/base_tts/")
async def base_tts(text: str, speed: Optional[float] = 1.0):
    """
    Perform text-to-speech conversion using only the base speaker.
    """
    try:
        # Use the default English voice
        return await synthesize_speech(text=text, voice="default_en", speed=speed)
    except Exception as e:
        logging.error(f"Error in base_tts: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/change_voice/")
async def change_voice(reference_speaker: str = Form(...), file: UploadFile = File(...)):
    """
    Change the voice of an existing audio file.
    """
    request_id = str(uuid.uuid4())[:8]
    try:
        logging.info(f'[{request_id}] Changing voice to {reference_speaker}...')

        contents = await file.read()

        # Save the input audio temporarily with unique filename
        input_path = f'{output_dir}/input_audio_{request_id}.wav'
        await run_in_thread(lambda: open(input_path, 'wb').write(contents))

        # Find the reference audio file
        matching_files = [file for file in os.listdir("resources") if file.startswith(str(reference_speaker))]
        if not matching_files:
            raise HTTPException(status_code=400, detail="No matching reference speaker found.")

        reference_file = f'resources/{matching_files[0]}'

        # Convert reference file to WAV if it's not already
        if not reference_file.lower().endswith('.wav'):
            ref_wav_path = f'{output_dir}/ref_converted_{request_id}.wav'
            await run_in_thread(convert_to_wav, reference_file, ref_wav_path)
            reference_file = ref_wav_path

        # Get model from pool
        model = await model_pool.get_model()
        try:
            # For voice conversion, we'll use the same text for both reference and generation
            text = await run_in_thread(model.transcribe, input_path)
            save_path = f'{output_dir}/output_converted_{request_id}.wav'

            # Run inference in thread pool
            def run_inference():
                return model.infer(
                    ref_file=reference_file,
                    ref_text=text,
                    gen_text=text,
                    file_wave=save_path
                )

            wav, sr, _ = await run_in_thread(run_inference)

            result = StreamingResponse(open(save_path, 'rb'), media_type="audio/wav")
            return result
        finally:
            # Always return model to pool
            await model_pool.return_model(model)

    except Exception as e:
        logging.error(f'[{request_id}] Error in change_voice: {str(e)}')
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/upload_audio/")
async def upload_audio(audio_file_label: str = Form(...), file: UploadFile = File(...)):
    """
    Upload an audio file for later use as the reference audio.
    """
    try:
        contents = await file.read()

        allowed_extensions = {'wav', 'mp3', 'flac', 'ogg'}
        max_file_size = 5 * 1024 * 1024  # 5MB

        if not file.filename.split('.')[-1] in allowed_extensions:
            return {"error": "Invalid file type. Allowed types are: wav, mp3, flac, ogg"}

        if len(contents) > max_file_size:
            return {"error": "File size is over limit. Max size is 5MB."}

        temp_file = io.BytesIO(contents)
        file_format = magic.from_buffer(temp_file.read(), mime=True)

        if 'audio' not in file_format:
            return {"error": "Invalid file content."}

        file_extension = file.filename.split('.')[-1]
        stored_file_name = f"{audio_file_label}.{file_extension}"

        with open(f"resources/{stored_file_name}", "wb") as f:
            f.write(contents)

        # Also create a WAV version for F5-TTS
        wav_path = f"resources/{audio_file_label}.wav"
        convert_to_wav(f"resources/{stored_file_name}", wav_path)

        return {"message": f"File {file.filename} uploaded successfully with label {audio_file_label}."}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/synthesize_speech/")
async def synthesize_speech(
        text: str,
        voice: str,
        speed: Optional[float] = 1.0,
):
    """
    Synthesize speech from text using a specified voice and style.
    """
    request_id = str(uuid.uuid4())[:8]
    start_time = time.time()
    try:
        logging.info(f'[{request_id}] Generating speech for {voice}')

        now = time.time()
        # First try to find a WAV version
        matching_files = [f for f in os.listdir("resources") if f.startswith(voice) and f.lower().endswith('.wav')]

        # If no WAV found, try other formats and convert
        if not matching_files:
            matching_files = [f for f in os.listdir("resources") if f.startswith(voice)]
            if not matching_files:
                raise HTTPException(status_code=400, detail="No matching voice found.")

            # Convert to WAV
            input_file = f'resources/{matching_files[0]}'
            wav_path = f'{output_dir}/ref_converted_{request_id}.wav'
            await run_in_thread(convert_to_wav, input_file, wav_path)
            reference_file = wav_path
        else:
            reference_file = f'resources/{matching_files[0]}'

        # Use default text for default voice, transcribe for others
        if voice == "default_en":
            ref_text = default_ref_text
        else:
            # Process reference audio with silence detection for natural clipping
            temp_short_ref = f'{output_dir}/temp_short_ref_{request_id}.wav'

            def process_audio():
                aseg = AudioSegment.from_file(reference_file)

                # 1. try to find long silence for clipping
                non_silent_segs = silence.split_on_silence(
                    aseg, min_silence_len=1000, silence_thresh=-50, keep_silence=1000, seek_step=10
                )
                non_silent_wave = AudioSegment.silent(duration=0)
                for non_silent_seg in non_silent_segs:
                    if len(non_silent_wave) > 6000 and len(non_silent_wave + non_silent_seg) > 15000:
                        logging.info(f"[{request_id}] Audio is over 15s, clipping short. (1)")
                        break
                    non_silent_wave += non_silent_seg

                # 2. try to find short silence for clipping if 1. failed
                if len(non_silent_wave) > 15000:
                    non_silent_segs = silence.split_on_silence(
                        aseg, min_silence_len=100, silence_thresh=-40, keep_silence=1000, seek_step=10
                    )
                    non_silent_wave = AudioSegment.silent(duration=0)
                    for non_silent_seg in non_silent_segs:
                        if len(non_silent_wave) > 6000 and len(non_silent_wave + non_silent_seg) > 15000:
                            logging.info(f"[{request_id}] Audio is over 15s, clipping short. (2)")
                            break
                        non_silent_wave += non_silent_seg

                aseg = non_silent_wave

                # 3. if no proper silence found for clipping
                if len(aseg) > 15000:
                    aseg = aseg[:15000]
                    logging.info(f"[{request_id}] Audio is over 15s, clipping short. (3)")

                aseg = remove_silence_edges(aseg) + AudioSegment.silent(duration=50)
                aseg.export(temp_short_ref, format='wav')

            await run_in_thread(process_audio)

            # Transcribe the short clip
            # ref_text = model.transcribe(temp_short_ref)
            ref_text = "And so, my fellow Americans, ask not what your country can do for you, ask what you can do for your country."
            logging.info(f'[{request_id}] Reference text transcribed from first 14s: {ref_text}')

            # Use the short clip as reference
            reference_file = temp_short_ref

        save_path = f'{output_dir}/output_synthesized_{request_id}.wav'
        logging.info(f'[{request_id}] Preprocessing time: {time.time() - now:.2f}s')

        # Get model from pool
        logging.info(f'[{request_id}] Requesting model from pool...')
        model = await model_pool.get_model()
        logging.info(f'[{request_id}] Model acquired, starting inference...')

        try:
            now = time.time()
            # Use the model's built-in text chunking and processing
            def run_inference():
                logging.info(f'[{request_id}] Starting model.infer() in thread...')
                result = model.infer(
                    ref_file=reference_file,
                    ref_text=ref_text,
                    gen_text=text,
                    speed=speed,
                    nfe_step=32,
                    cfg_strength=2.0,
                    file_wave=save_path
                )
                logging.info(f'[{request_id}] model.infer() completed in thread')
                return result

            wav, sr, _ = await run_in_thread(run_inference)
            logging.info(f'[{request_id}] Inference time: {time.time() - now:.2f}s')

            result = StreamingResponse(open(save_path, 'rb'), media_type="audio/wav")

            end_time = time.time()
            elapsed_time = end_time - start_time

            result.headers["X-Elapsed-Time"] = str(elapsed_time)
            result.headers["X-Device-Used"] = device
            result.headers["X-Request-ID"] = request_id

            # Add CORS headers
            result.headers["Access-Control-Allow-Origin"] = "*"
            result.headers["Access-Control-Allow-Credentials"] = "true"
            result.headers["Access-Control-Allow-Headers"] = "Origin, Content-Type, X-Amz-Date, Authorization, X-Api-Key, X-Amz-Security-Token, locale"
            result.headers["Access-Control-Allow-Methods"] = "POST, OPTIONS"

            logging.info(f'[{request_id}] Total processing time: {elapsed_time:.2f}s')
            return result

        finally:
            # Always return model to pool
            await model_pool.return_model(model)

    except Exception as e:
        logging.error(f'[{request_id}] Error in synthesize_speech: {str(e)}')
        raise HTTPException(status_code=500, detail=str(e))
