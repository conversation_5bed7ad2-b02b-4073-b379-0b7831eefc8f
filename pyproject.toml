[build-system]
requires = ["setuptools >= 61.0", "setuptools-scm>=8.0"]
build-backend = "setuptools.build_meta"

[project]
name = "f5-tts"
version = "0.4.2"
description = "F5-TTS: A Fairytaler that Fakes Fluent and Faithful Speech with Flow Matching"
readme = "README.md"
license = {text = "MIT License"}
classifiers = [
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
]
dependencies = [
    "accelerate>=0.33.0",
    "bitsandbytes>0.37.0; platform_machine != 'arm64' and platform_system != 'Darwin'",
    "cached_path",
    "click",
    "datasets",
    "ema_pytorch>=0.5.2",
    "gradio>=3.45.2",
    "hydra-core>=1.3.0",
    "jieba",
    "librosa",
    "matplotlib",
    "numpy<=1.26.4",
    "pydub",
    "pypinyin",
    "safetensors",
    "soundfile",
    "tomli",
    "torch>=2.0.0",
    "torchaudio>=2.0.0",
    "torchdiffeq",
    "tqdm>=4.65.0",
    "transformers",
    "transformers_stream_generator",
    "vocos",
    "wandb",
    "x_transformers>=1.31.14",
]

[project.optional-dependencies]
eval = [
    "faster_whisper==0.10.1",
    "funasr",
    "jiwer",
    "modelscope",
    "zhconv",
    "zhon",
]

[project.urls]
Homepage = "https://github.com/SWivid/F5-TTS"

[project.scripts]
"f5-tts_infer-cli" = "f5_tts.infer.infer_cli:main"
"f5-tts_infer-gradio" = "f5_tts.infer.infer_gradio:main"
"f5-tts_finetune-cli" = "f5_tts.train.finetune_cli:main"
"f5-tts_finetune-gradio" = "f5_tts.train.finetune_gradio:main"
