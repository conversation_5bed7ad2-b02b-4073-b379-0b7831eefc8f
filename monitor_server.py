#!/usr/bin/env python3
"""
Script để monitor performance của F5-TTS server
"""

import asyncio
import aiohttp
import time
import json
import psutil
import subprocess
import sys

SERVER_URL = "http://localhost:7860"

def get_gpu_info():
    """Lấy thông tin GPU sử dụng nvidia-smi"""
    try:
        result = subprocess.run([
            'nvidia-smi', 
            '--query-gpu=memory.used,memory.total,utilization.gpu,temperature.gpu',
            '--format=csv,noheader,nounits'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            gpu_info = []
            for i, line in enumerate(lines):
                parts = line.split(', ')
                if len(parts) >= 4:
                    gpu_info.append({
                        'gpu_id': i,
                        'memory_used': int(parts[0]),
                        'memory_total': int(parts[1]),
                        'utilization': int(parts[2]),
                        'temperature': int(parts[3])
                    })
            return gpu_info
    except Exception as e:
        return [{"error": str(e)}]
    
    return []

def get_system_info():
    """Lấy thông tin hệ thống"""
    return {
        'cpu_percent': psutil.cpu_percent(interval=1),
        'memory_percent': psutil.virtual_memory().percent,
        'memory_used_gb': psutil.virtual_memory().used / (1024**3),
        'memory_total_gb': psutil.virtual_memory().total / (1024**3),
        'disk_percent': psutil.disk_usage('/').percent
    }

async def get_server_health(session):
    """Lấy thông tin health từ server"""
    try:
        async with session.get(f"{SERVER_URL}/health/", timeout=5) as response:
            if response.status == 200:
                return await response.json()
            else:
                return {"error": f"HTTP {response.status}"}
    except Exception as e:
        return {"error": str(e)}

async def test_request_speed(session):
    """Test tốc độ xử lý một request"""
    try:
        start_time = time.time()
        params = {
            'text': 'This is a quick test message.',
            'voice': 'default_en',
            'speed': 1.0
        }
        
        async with session.get(f"{SERVER_URL}/synthesize_speech/", params=params, timeout=30) as response:
            if response.status == 200:
                content = await response.read()
                end_time = time.time()
                return {
                    'success': True,
                    'response_time': end_time - start_time,
                    'content_length': len(content),
                    'server_time': response.headers.get('X-Elapsed-Time', 'N/A')
                }
            else:
                return {'success': False, 'error': f"HTTP {response.status}"}
    except Exception as e:
        return {'success': False, 'error': str(e)}

async def monitor_loop():
    """Main monitoring loop"""
    print("F5-TTS Server Monitor")
    print("=" * 50)
    print("Press Ctrl+C to stop")
    print()
    
    async with aiohttp.ClientSession() as session:
        while True:
            try:
                print(f"\n[{time.strftime('%H:%M:%S')}] Server Status")
                print("-" * 30)
                
                # Server health
                health = await get_server_health(session)
                if 'error' not in health:
                    print(f"✅ Server: Online")
                    print(f"   Model Pool: {'✅ Ready' if health.get('model_pool_initialized') else '❌ Not Ready'}")
                    print(f"   Available Models: {health.get('available_models', 'N/A')}/{health.get('total_models', 'N/A')}")
                    print(f"   Device: {health.get('device', 'N/A')}")
                else:
                    print(f"❌ Server: {health['error']}")
                
                # System info
                sys_info = get_system_info()
                print(f"\n💻 System Resources:")
                print(f"   CPU: {sys_info['cpu_percent']:.1f}%")
                print(f"   RAM: {sys_info['memory_percent']:.1f}% ({sys_info['memory_used_gb']:.1f}GB/{sys_info['memory_total_gb']:.1f}GB)")
                print(f"   Disk: {sys_info['disk_percent']:.1f}%")
                
                # GPU info
                gpu_info = get_gpu_info()
                if gpu_info and 'error' not in gpu_info[0]:
                    print(f"\n🎮 GPU Status:")
                    for gpu in gpu_info:
                        memory_percent = (gpu['memory_used'] / gpu['memory_total']) * 100
                        print(f"   GPU {gpu['gpu_id']}: {memory_percent:.1f}% ({gpu['memory_used']}MB/{gpu['memory_total']}MB)")
                        print(f"   Utilization: {gpu['utilization']}%, Temp: {gpu['temperature']}°C")
                else:
                    print(f"\n🎮 GPU: Not available or error")
                
                # Test request speed (every 3rd iteration)
                if int(time.time()) % 30 < 10:  # Test every ~30 seconds
                    print(f"\n⚡ Testing Request Speed...")
                    speed_test = await test_request_speed(session)
                    if speed_test['success']:
                        print(f"   Response Time: {speed_test['response_time']:.2f}s")
                        print(f"   Server Time: {speed_test['server_time']}s")
                        print(f"   Content Size: {speed_test['content_length']} bytes")
                    else:
                        print(f"   ❌ Test Failed: {speed_test['error']}")
                
                print(f"\nNext update in 10 seconds...")
                await asyncio.sleep(10)
                
            except KeyboardInterrupt:
                print("\n\nMonitoring stopped by user")
                break
            except Exception as e:
                print(f"\n❌ Monitor Error: {e}")
                await asyncio.sleep(5)

def main():
    """Main function"""
    if len(sys.argv) > 1 and sys.argv[1] == '--once':
        # Chạy một lần rồi thoát
        async def run_once():
            async with aiohttp.ClientSession() as session:
                health = await get_server_health(session)
                sys_info = get_system_info()
                gpu_info = get_gpu_info()
                
                print("Server Health:", json.dumps(health, indent=2))
                print("System Info:", json.dumps(sys_info, indent=2))
                print("GPU Info:", json.dumps(gpu_info, indent=2))
        
        asyncio.run(run_once())
    else:
        # Chạy continuous monitoring
        try:
            asyncio.run(monitor_loop())
        except KeyboardInterrupt:
            print("\nGoodbye!")

if __name__ == "__main__":
    main()
